import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { ScoringInfo } from '../ScoringInfo/ScoringInfo';
import { useDataHealth } from '../../data-health-heatmap/DataHealthContext';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  const { recordType } = useDataHealth();
  const showScorinIndicator = recordType === 'issues';
  return (
    <div className="overflow-x-auto">
      <div className={cn('flex flex-row px-4 min-w-max items-center justify-end', { 'justify-between': showScorinIndicator })}>
        <ScoringInfo />
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <div key={level} className="p-2 last:pr-0">
                <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
