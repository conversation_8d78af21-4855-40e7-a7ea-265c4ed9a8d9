import { useMessageGetter } from "@messageformat/react";
import Button from "@shape-construction/arch-ui/src/Button";
import { InformationCircleIcon } from "@shape-construction/arch-ui/src/Icons/solid";
import Popover from "@shape-construction/arch-ui/src/Popover";
import { Tooltip, TooltipContent, TooltipTrigger } from "@shape-construction/arch-ui/src/Tooltip/Tooltip";
import { breakpoints } from "@shape-construction/arch-ui/src/utils/breakpoints";
import { useMediaQuery } from "@shape-construction/hooks";
import { IssuesScoringInfo } from "./IssuesScoringInfo";

export const IssuesScoringInfoPopover: React.FC = () => {
    const messages = useMessageGetter('scoringInfo');
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));
    return <Popover>
        <Popover.Trigger>
            <Tooltip delayDuration={100}>
                <TooltipTrigger className="flex items-center">
                    {isLargeScreen ? (
                        <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                            {messages('scoringInfoCTA')}
                        </Button>
                    ) : (
                        <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon} />
                    )}
                </TooltipTrigger>
                <TooltipContent align="start">
                    {messages('tooltipText')}
                </TooltipContent>
            </Tooltip>
        </Popover.Trigger>
        <Popover.Content align="start" className="p-0 max-h-[320px]">
            <IssuesScoringInfo />
        </Popover.Content>
    </Popover>
};
