// write tests

import { render, screen } from 'tests/test-utils';
import { ScoringInfo } from './ScoringInfo';

describe('<ScoringInfo />', () => {
  it('renders the component', () => {
    render(<ScoringInfo />);

    expect(screen.getByText('scoringInfoCTA')).toBeInTheDocument();
  });

  describe('when recordType is issues', () => {
    it('renders the component', () => {
      render(<ScoringInfo />, {
        preloadedState: {
          dataHealth: {
            recordType: 'issues',
          },
        },
      });

      expect(screen.getByText('scoringInfoCTA')).toBeInTheDocument();
    });
  });

});
