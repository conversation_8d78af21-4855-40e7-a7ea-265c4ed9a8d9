import React from 'react';
import { dataHealthRecordTypeEnum } from '@shape-construction/api/src/types';
import { render, screen } from 'tests/test-utils';
import { DataHealthProvider } from '../../data-health-heatmap/DataHealthContext';
import { ScoringInfo } from './ScoringInfo';

// Mock the IssuesScoringInfoPopover component
jest.mock('./IssuesScoringInfoPopover', () => ({
    IssuesScoringInfoPopover: () => <div data-testid="issues-scoring-info-popover">Issues Scoring Info</div>,
}));

describe('<ScoringInfo />', () => {
    const renderWithDataHealthProvider = (recordType: 'issues' | 'shift_reports') => {
        return render(
            <DataHealthProvider recordType={recordType} seriesLength={3}>
                <ScoringInfo />
            </DataHealthProvider>
        );
    };

    describe('when recordType is "issues"', () => {
        it('renders the IssuesScoringInfoPopover component', () => {
            renderWithDataHealthProvider(dataHealthRecordTypeEnum.issues);

            expect(screen.getByTestId('issues-scoring-info-popover')).toBeInTheDocument();
            expect(screen.getByText('Issues Scoring Info')).toBeInTheDocument();
        });
    });

    describe('when recordType is "shift_reports"', () => {
        it('renders nothing (null)', () => {
            const { container } = renderWithDataHealthProvider(dataHealthRecordTypeEnum.shift_reports);

            expect(container.firstChild).toBeNull();
        });
    });

    describe('when used outside DataHealthProvider', () => {
        it('throws an error', () => {
            // Suppress console.error for this test since we expect an error
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

            expect(() => {
                render(<ScoringInfo />);
            }).toThrow('useDataHealth must be used within a DataHealthProvider');

            consoleSpy.mockRestore();
        });
    });
});
